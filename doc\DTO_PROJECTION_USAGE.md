# DTO投影使用说明

## 概述
在`getLabelWorkProcessAnalysisByMultiplePlIds`方法中，我们使用了DTO投影模式来优化查询性能和提供更好的类型安全性。

## DTO投影的优势

### 1. 类型安全
- 避免了直接操作`Object[]`数组的类型转换错误
- 编译时检查字段类型匹配
- IDE提供更好的代码提示和重构支持

### 2. 性能优化
- 减少内存占用，只加载需要的字段
- 避免JPA实体的懒加载问题
- 更快的序列化和反序列化

### 3. 代码可维护性
- 清晰的数据传输对象结构
- 便于单元测试和模拟
- 更好的代码可读性

## 实现方式

### 1. DTO类定义
```java
public class LabelWorkProcessAnalysisDto {
    private String lbId;
    private String lbGrp;
    private String plId;
    private String mo;
    private String keyWpFirstResult;
    private String finalResult;
    private Date finalWpCmpDate;

    // 构造函数用于投影
    public LabelWorkProcessAnalysisDto(String lbId, String lbGrp, String plId, String mo, 
            String keyWpFirstResult, String finalResult, Date finalWpCmpDate) {
        // 初始化字段
    }

    // 转换为实体类的方法
    public LabelWorkProcessAnalysis toEntity() {
        return new LabelWorkProcessAnalysis(
            this.lbId, this.lbGrp, this.plId, this.mo, 
            this.keyWpFirstResult, this.finalResult, this.finalWpCmpDate
        );
    }
}
```

### 2. Repository中的使用
```java
public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysisByMultiplePlIds(
        List<String> plIds, Date startDate, Date endDate) {
    
    // 执行原生SQL查询
    Query query = entityManager.createNativeQuery(sql);
    List<Object[]> resultList = query.getResultList();
    
    // 使用DTO投影进行结果映射
    return resultList.stream()
        .map(row -> new LabelWorkProcessAnalysisDto(
            (String) row[0], // LB_ID
            (String) row[1], // LB_GRP
            (String) row[2], // PL_ID
            (String) row[3], // MO
            row[4].toString(), // KEY_WP_FIRST_RESULT
            row[5].toString(), // FINAL_RESULT
            (Date) row[6] // FINAL_WP_CMP_DATE
        ))
        .map(LabelWorkProcessAnalysisDto::toEntity)
        .toList();
}
```

## 使用场景

### 适用场景
1. **复杂查询**: 涉及多表连接和聚合函数的查询
2. **性能敏感**: 需要优化查询性能的场景
3. **大数据量**: 处理大量数据时减少内存占用
4. **API响应**: 需要特定格式数据的API接口

### 不适用场景
1. **简单查询**: 单表简单查询可以直接使用实体类
2. **完整实体**: 需要完整实体对象进行业务操作的场景
3. **频繁修改**: 查询结果字段经常变化的场景

## 最佳实践

### 1. 命名规范
- DTO类名以`Dto`结尾
- 字段名与数据库列名保持一致
- 方法名清晰表达用途

### 2. 构造函数设计
- 提供用于投影的构造函数
- 参数顺序与SQL查询字段顺序一致
- 添加必要的参数验证

### 3. 转换方法
- 提供`toEntity()`方法转换为领域实体
- 考虑提供静态工厂方法
- 处理可能的空值情况

### 4. 文档和注释
- 为DTO类添加清晰的文档说明
- 标注字段对应的数据库列
- 说明使用场景和限制

## 性能对比

| 方式 | 内存占用 | 查询速度 | 类型安全 | 可维护性 |
|------|----------|----------|----------|----------|
| 直接Object[] | 低 | 快 | 差 | 差 |
| DTO投影 | 中 | 快 | 好 | 好 |
| 完整实体 | 高 | 慢 | 好 | 中 |

## 注意事项

1. **字段顺序**: DTO构造函数参数顺序必须与SQL查询字段顺序一致
2. **类型转换**: 注意数据库类型与Java类型的转换
3. **空值处理**: 考虑数据库字段可能为空的情况
4. **性能测试**: 在实际环境中测试性能提升效果
5. **版本兼容**: 确保DTO结构变更不影响现有功能

## 扩展建议

1. **通用DTO基类**: 创建通用的DTO基类包含公共字段
2. **Builder模式**: 对于复杂DTO可以考虑使用Builder模式
3. **验证注解**: 添加Bean Validation注解进行数据验证
4. **序列化优化**: 为API响应优化JSON序列化配置
