package com.hongjing.oee_mes.repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import com.hongjing.oee_mes.config.QueryConfig;
import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.dto.LabelWorkProcessAnalysisDto;


/**
 * 标签工艺过程分析Repository 包含标签工艺过程分析相关的查询方法
 */
@Repository
@SuppressWarnings("unchecked")
public class LabelWorkProcessAnalysisRepository {

	@PersistenceContext
	private EntityManager entityManager;

	private final JdbcTemplate jdbcTemplate;

	private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	private final QueryConfig queryConfig;

	public LabelWorkProcessAnalysisRepository(JdbcTemplate jdbcTemplate, QueryConfig queryConfig,
			NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.queryConfig = queryConfig;
		this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
	}

	/**
	 * LabelWorkProcessAnalysisDto 的 RowMapper
	 */
	private static final RowMapper<LabelWorkProcessAnalysisDto> LABEL_WORK_PROCESS_ANALYSIS_ROW_MAPPER = new RowMapper<LabelWorkProcessAnalysisDto>() {
		@Override
		public LabelWorkProcessAnalysisDto mapRow(ResultSet rs, int rowNum) throws SQLException {
			return new LabelWorkProcessAnalysisDto(rs.getString("LB_ID"), rs.getString("LB_GRP"), rs.getString("PL_ID"),
					rs.getString("MO"), rs.getString("KEY_WP_FIRST_RESULT"), rs.getString("FINAL_RESULT"),
					rs.getTimestamp("FINAL_WP_CMP_DATE"));
		}
	};

	/**
	 * 根据线体ID和日期查询标签工艺过程分析数据 查询失败的标签（MIN(IS_PASS) = 'N'）及其相关统计信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate) {
		String sql = """
				SELECT
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO,
				    MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) AS KEY_WP_FIRST_RESULT,
				    MAX(A.IS_PASS) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_RESULT,
				    MAX(A.WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_WP_CMP_DATE
				FROM
				    TB_PM_MO_LBWP A
				INNER JOIN TB_PM_MO_LB B
				ON A.LB_ID = B.LB_ID
				WHERE
				    A.WP_CMP_DATE >= :startDate
				    AND A.WP_CMP_DATE < :endDate
				    AND A.PL_ID = :plId
				GROUP BY
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO
				HAVING
				     MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) = 'N'
				ORDER BY
				    FINAL_WP_CMP_DATE
				""";

		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new LabelWorkProcessAnalysis((String) row[0], // LB_ID
					(String) row[1], // LB_GRP
					(String) row[2], // PL_ID
					(String) row[3], // MO
					row[4].toString(), // KEY_WP_FIRST_RESULT
					row[5].toString(), // final_result
					(Date) row[6] // FINAL_WP_CMP_DATE
			))
			.toList();
	}

	/**
	 * 根据多个线体ID和日期查询标签工艺过程分析数据 使用JdbcTemplate原生查询，支持多个线体ID的查询 使用DTO投影优化查询性能和类型安全
	 * 自动处理Oracle IN子句1000参数限制
	 * @param plIds 线体ID列表
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysisByMultiplePlIds(List<String> plIds, Date startDate,
			Date endDate) {
		if (plIds == null || plIds.isEmpty()) {
			return List.of();
		}

		// 使用ROW_NUMBER()分析函数重写SQL，以获得更稳定和高效的执行计划。
		// 使用CTE (WITH clause) 使逻辑更清晰。
		String sql = """
				SELECT
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO,
				    MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) AS KEY_WP_FIRST_RESULT,
				    MAX(A.IS_PASS) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_RESULT,
				    MAX(A.WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_WP_CMP_DATE
				FROM
				    TB_PM_MO_LBWP A
				INNER JOIN TB_PM_MO_LB B
				ON A.LB_ID = B.LB_ID
				WHERE
				    A.WP_CMP_DATE >= :startDate
				    AND A.WP_CMP_DATE < :endDate
				    AND A.PL_ID in (:plIds)
				GROUP BY
				    A.LB_ID,
				    B.LB_GRP,
				    A.PL_ID,
				    A.MO
				HAVING
				     MIN(
				        CASE
				            WHEN A.WP_ID IN ('SMT-03', 'SMT-06')
				            THEN A.IS_PASS
				            ELSE 'Y'
				        END
				    ) = 'N'
				ORDER BY
				    FINAL_WP_CMP_DATE
				""";

		// 使用 MapSqlParameterSource 来设置命名参数，非常适合处理 IN 子句
		MapSqlParameterSource parameters = new MapSqlParameterSource();
		parameters.addValue("startDate", startDate);
		parameters.addValue("endDate", endDate);
		parameters.addValue("plIds", plIds); // NamedParameterJdbcTemplate会自动处理List

		// 使用 NamedParameterJdbcTemplate 执行查询
		List<LabelWorkProcessAnalysisDto> results = namedParameterJdbcTemplate.query(sql, parameters,
				LABEL_WORK_PROCESS_ANALYSIS_ROW_MAPPER // 你的RowMapper保持不变
		);

		// 转换并返回最终结果
		return results.stream().map(LabelWorkProcessAnalysisDto::toEntity).toList();
	}

	/**
	 * 根据LB_ID列表查询QC不良项目信息
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
		if (lbIds == null || lbIds.isEmpty()) {
			return List.of();
		}

		// Oracle IN子句最多支持1000个参数，需要分批查询
		final int BATCH_SIZE = queryConfig.getBatchSize();
		List<QcBadItem> allResults = new ArrayList<>();

		for (int i = 0; i < lbIds.size(); i += BATCH_SIZE) {
			int endIndex = Math.min(i + BATCH_SIZE, lbIds.size());
			List<String> batchLbIds = lbIds.subList(i, endIndex);

			String sql = """
					SELECT
					    A.MO, A.PL_ID, A.LB_ID, B.BAD_ITEM_ID, B.BAD_POINT, B.FIELD_EX2
					FROM
					    TB_PM_QC_HD A
					    INNER JOIN
					    TB_PM_QC_DT B
					   ON A.QC_ID = B.QC_ID
					WHERE
					    A.LB_ID IN (:lbIds)
					""";

			Query query = entityManager.createNativeQuery(sql);
			query.setParameter("lbIds", batchLbIds);
			List<Object[]> resultList = query.getResultList();

			List<QcBadItem> batchResults = resultList.stream()
				.map(row -> new QcBadItem((String) row[0], // MO
						(String) row[1], // PL_ID
						(String) row[2], // LB_ID
						(String) row[3], // BAD_ITEM_ID
						(String) row[4], // BAD_POINT
						(String) row[5] // FIELD_EX2
				))
				.toList();

			allResults.addAll(batchResults);
		}

		return allResults;
	}

	/**
	 * 使用临时表方式查询QC不良项目信息（适用于大量LB_ID的场景）
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	public List<QcBadItem> getQcBadItemsByLbIdsUsingTempTable(List<String> lbIds) {
		if (lbIds == null || lbIds.isEmpty()) {
			return List.of();
		}

		// 创建临时表
		String createTempTableSql = """
				CREATE GLOBAL TEMPORARY TABLE TEMP_LB_IDS (
				    LB_ID VARCHAR2(100)
				) ON COMMIT DELETE ROWS
				""";

		// 插入数据到临时表
		String insertSql = "INSERT INTO TEMP_LB_IDS (LB_ID) VALUES (?)";

		// 查询SQL
		String querySql = """
				SELECT
				    A.MO, A.PL_ID, A.LB_ID, B.BAD_ITEM_ID, B.BAD_POINT, B.FIELD_EX2
				FROM
				    TB_PM_QC_HD A
				    INNER JOIN TB_PM_QC_DT B ON A.QC_ID = B.QC_ID
				    INNER JOIN TEMP_LB_IDS T ON A.LB_ID = T.LB_ID
				""";

		// 删除临时表
		String dropTempTableSql = "DROP TABLE TEMP_LB_IDS";

		try {
			// 创建临时表
			entityManager.createNativeQuery(createTempTableSql).executeUpdate();

			// 批量插入LB_ID
			Query insertQuery = entityManager.createNativeQuery(insertSql);
			for (String lbId : lbIds) {
				insertQuery.setParameter(1, lbId);
				insertQuery.executeUpdate();
			}

			// 执行查询
			Query query = entityManager.createNativeQuery(querySql);
			List<Object[]> resultList = query.getResultList();

			return resultList.stream()
				.map(row -> new QcBadItem((String) row[0], // MO
						(String) row[1], // PL_ID
						(String) row[2], // LB_ID
						(String) row[3], // BAD_ITEM_ID
						(String) row[4], // BAD_POINT
						(String) row[5] // FIELD_EX2
				))
				.toList();

		}
		catch (Exception e) {
			// 确保在异常情况下也能清理临时表
			try {
				entityManager.createNativeQuery(dropTempTableSql).executeUpdate();
			}
			catch (Exception cleanupException) {
				// 记录清理异常，但不抛出，避免掩盖原始异常
				System.err.println("Failed to cleanup temp table: " + cleanupException.getMessage());
			}
			throw new RuntimeException("Failed to query QC bad items using temp table", e);
		}
		finally {
			// 最终清理临时表
			try {
				entityManager.createNativeQuery(dropTempTableSql).executeUpdate();
			}
			catch (Exception e) {
				// 忽略清理异常，可能表已经不存在
			}
		}
	}

}
