package com.hongjing.oee_mes.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 查询配置类 用于配置数据库查询相关的参数
 */
@Configuration
@ConfigurationProperties(prefix = "oee.query")
public class QueryConfig {

	/**
	 * Oracle IN子句的最大参数数量
	 */
	private int oracleInClauseMaxSize = 1000;

	/**
	 * 使用临时表查询的阈值 当LB_ID数量超过此值时，使用临时表方式查询
	 */
	private int tempTableThreshold = 5000;

	/**
	 * 分批查询的批次大小
	 */
	private int batchSize = 1000;

	public int getOracleInClauseMaxSize() {
		return oracleInClauseMaxSize;
	}

	public void setOracleInClauseMaxSize(int oracleInClauseMaxSize) {
		this.oracleInClauseMaxSize = oracleInClauseMaxSize;
	}

	public int getTempTableThreshold() {
		return tempTableThreshold;
	}

	public void setTempTableThreshold(int tempTableThreshold) {
		this.tempTableThreshold = tempTableThreshold;
	}

	public int getBatchSize() {
		return batchSize;
	}

	public void setBatchSize(int batchSize) {
		this.batchSize = batchSize;
	}

}
