server:
  port: 9996
  servlet:
    context-path: /oee-mes-server
spring:
  application:
    name: oee-mes
  datasource:
    url: ******************************************
    username: ims
    password: imsorcl123
    driver-class-name: oracle.jdbc.OracleDriver
    # HikariCP 是 Spring Boot 2.x 及更高版本的默认连接池，无需显式指定 type
    # type: com.zaxxer.hikari.HikariDataSource
    
    # HikariCP 连接池详细配置
    hikari:
      # 连接池名称，便于监控，这是一个好习惯
      pool-name: SpringBootOracleCP
      
      # 性能优化关键：保持最小和最大连接数一致，创建固定大小的连接池。
      # 这可以避免连接的动态创建和销毁，在高负载下性能更好。
      # 请根据您的服务器和数据库能力进行基准测试来确定最终值。
      minimum-idle: 20
      maximum-pool-size: 20
      
      # 连接超时时间（毫秒），30秒是行业标准默认值，通常无需更改
      connection-timeout: 30000
      
      # 空闲连接超时时间（毫秒），仅当 minimum-idle < maximum-pool-size 时才有效
      # 在固定大小的池中，此设置基本不起作用，可以保留或移除
      idle-timeout: 600000
      
      # 连接最大生命周期（毫秒），30分钟是一个安全值，防止因网络设备策略导致连接失效
      max-lifetime: 1800000
      
      # 无需设置，默认为 true，由 Spring 的事务管理控制
      # auto-commit: true
      
      # 优化关键：强烈建议移除此项。
      # 现代 Oracle JDBC 驱动 (ojdbc8+) 支持 JDBC4 Connection.isValid() API。
      # HikariCP 会自动使用此高效方法。设置 test-query 会强制执行 SQL 查询，反而降低性能。
      # connection-test-query: SELECT 1 FROM DUAL
      
  # Spring Data JPA 配置
  jpa:
    database: oracle
    properties:
      hibernate:
        # 正确的配置：为 Oracle 11g 指定了兼容的方言，非常重要
        dialect: org.hibernate.dialect.Oracle10gDialect
    
    # 生产环境强烈建议设置为 false，避免性能损耗和日志泛滥
    show-sql: true
