package com.hongjing.oee_mes.service;

import java.util.Date;
import java.util.List;

import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.LabelWorkProcessWithQcBadItems;
import com.hongjing.oee_mes.domain.QcBadItem;

/**
 * 标签工艺过程分析服务接口
 */
public interface LabelWorkProcessAnalysisService {

	/**
	 * 根据线体ID和日期查询标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(String plId, Date startDate, Date endDate);

	/**
	 * 根据多个线体ID和日期查询标签工艺过程分析数据
	 * @param plIds 线体ID列表
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析列表
	 */
	List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysisByMultiplePlIds(List<String> plIds, Date startDate,
			Date endDate);

	/**
	 * 根据LB_ID列表查询QC不良项目信息
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds);

	/**
	 * 使用临时表方式查询QC不良项目信息（适用于大量LB_ID的场景）
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	List<QcBadItem> getQcBadItemsByLbIdsUsingTempTable(List<String> lbIds);

	/**
	 * 根据多个线体ID和日期查询标签工艺过程分析数据，并包含QC不良项目信息
	 * @param plIds 线体ID列表
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 标签工艺过程分析与QC不良项目组合列表
	 */
	List<LabelWorkProcessWithQcBadItems> getLabelWorkProcessAnalysisWithQcBadItemsByMultiplePlIds(List<String> plIds,
			Date startDate, Date endDate);

}
