package com.hongjing.oee_mes.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis;
import com.hongjing.oee_mes.domain.LabelWorkProcessWithQcBadItems;
import com.hongjing.oee_mes.domain.QcBadItem;
import com.hongjing.oee_mes.service.LabelWorkProcessAnalysisService;

/**
 * 标签工艺过程分析控制器 提供标签工艺过程分析相关的REST API接口
 */
@RestController
@RequestMapping("/label-work-process-analysis")
public class LabelWorkProcessAnalysisController {

	private final LabelWorkProcessAnalysisService service;

	public LabelWorkProcessAnalysisController(LabelWorkProcessAnalysisService service) {
		this.service = service;
	}

	/**
	 * 根据单个线体ID和日期查询标签工艺过程分析数据
	 * @param plId 线体ID
	 * @param startDateStr 开始日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @param endDateStr 结束日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @return 标签工艺过程分析列表
	 * @throws ParseException 日期解析异常
	 */
	@GetMapping("/single-line")
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysis(@RequestParam("plId") String plId,
			@RequestParam("startDate") String startDateStr, @RequestParam("endDate") String endDateStr)
			throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return service.getLabelWorkProcessAnalysis(plId, startDate, endDate);
	}

	/**
	 * 根据多个线体ID和日期查询标签工艺过程分析数据 这是您提供的新查询接口
	 * @param plIds 线体ID列表
	 * @param startDateStr 开始日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @param endDateStr 结束日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @return 标签工艺过程分析列表
	 * @throws ParseException 日期解析异常
	 */
	@PostMapping("/multiple-lines")
	public List<LabelWorkProcessAnalysis> getLabelWorkProcessAnalysisByMultiplePlIds(@RequestBody List<String> plIds,
			@RequestParam("startDate") String startDateStr, @RequestParam("endDate") String endDateStr)
			throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return service.getLabelWorkProcessAnalysisByMultiplePlIds(plIds, startDate, endDate);
	}

	/**
	 * 根据LB_ID列表查询QC不良项目信息
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	@PostMapping("/qc-bad-items")
	public List<QcBadItem> getQcBadItemsByLbIds(@RequestBody List<String> lbIds) {
		return service.getQcBadItemsByLbIds(lbIds);
	}

	/**
	 * 使用临时表方式查询QC不良项目信息（适用于大量LB_ID的场景）
	 * @param lbIds LB_ID列表
	 * @return QC不良项目列表
	 */
	@PostMapping("/qc-bad-items-temp-table")
	public List<QcBadItem> getQcBadItemsByLbIdsUsingTempTable(@RequestBody List<String> lbIds) {
		return service.getQcBadItemsByLbIdsUsingTempTable(lbIds);
	}

	/**
	 * 根据多个线体ID和日期查询标签工艺过程分析数据，并包含QC不良项目信息
	 * @param plIds 线体ID列表
	 * @param startDateStr 开始日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @param endDateStr 结束日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
	 * @return 标签工艺过程分析与QC不良项目组合列表
	 * @throws ParseException 日期解析异常
	 */
	@PostMapping("/multiple-lines-with-qc-bad-items")
	public List<LabelWorkProcessWithQcBadItems> getLabelWorkProcessAnalysisWithQcBadItemsByMultiplePlIds(
			@RequestBody List<String> plIds, @RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr) throws ParseException {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date startDate = dateFormat.parse(startDateStr);
		Date endDate = dateFormat.parse(endDateStr);
		return service.getLabelWorkProcessAnalysisWithQcBadItemsByMultiplePlIds(plIds, startDate, endDate);
	}

}
