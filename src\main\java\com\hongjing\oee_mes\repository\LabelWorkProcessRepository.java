package com.hongjing.oee_mes.repository;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.hongjing.oee_mes.domain.LabelLatestStatus;

@Repository
@SuppressWarnings("unchecked")
public class LabelWorkProcessRepository {

	@PersistenceContext
	private EntityManager entityManager;

	/**
	 * 根据 LB_ID 列表，查询每个 LB_ID 的最新状态。 查询逻辑： 1. 找到每个 LB_ID 按 WP_CMP_DATE 排序的最新一条记录。 2.
	 * 如果该最新记录的 NEXT_WP_ID 为 NULL，则返回其 IS_PASS 状态。 3. 否则，不返回该 LB_ID 的任何信息。
	 * @param lbIds LB_ID 列表
	 * @return 返回符合条件的最新状态列表
	 */
	public List<LabelLatestStatus> getLatestStatusByLbIds(List<String> lbIds) {
		String sql = """
				SELECT
				    LB_ID,
				    LATEST_IS_PASS
				FROM (
				    -- 步骤1: 找到每个 LB_ID 的最新记录的 IS_PASS 和 NEXT_WP_ID
				    SELECT
				        LB_ID,
				        MAX(IS_PASS) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS LATEST_IS_PASS,
				        MAX(NEXT_WP_ID) KEEP (DENSE_RANK LAST ORDER BY WP_CMP_DATE) AS LATEST_NEXT_WP_ID
				    FROM
				        TB_PM_MO_LBWP
				    WHERE
				        LB_ID IN (:lbIds)
				    GROUP BY
				        LB_ID
				)
				-- 步骤2: 只筛选出最新记录的 NEXT_WP_ID 为 NULL 的结果
				WHERE
				    LATEST_NEXT_WP_ID IS NULL
				""";

		Query query = entityManager.createNativeQuery(sql); // 返回 Object[]
		query.setParameter("lbIds", lbIds);
		List<Object[]> results = query.getResultList();
		return results.stream().map(row -> new LabelLatestStatus(row[0].toString(), row[1].toString())).toList();
	}

}