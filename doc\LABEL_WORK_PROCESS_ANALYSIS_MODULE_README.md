# 标签工艺过程分析模块

## 概述
本模块提供标签工艺过程分析相关的功能，包括单线体查询、多线体查询以及QC不良项目查询。该模块是对原有OeeRepository中相关方法的重构和扩展。

## 新增功能
1. **多线体查询**: 支持同时查询多个线体的标签工艺过程分析数据
2. **独立模块**: 将标签工艺过程分析相关功能独立成单独的模块
3. **优化查询**: 自动处理Oracle IN子句1000参数限制
4. **智能策略**: 根据数据量自动选择最优查询策略

## 文件结构

### Repository层
- `LabelWorkProcessAnalysisRepository.java`: 标签工艺过程分析数据访问层
  - `getLabelWorkProcessAnalysis()`: 单线体查询
  - `getLabelWorkProcessAnalysisByMultiplePlIds()`: 多线体查询（新增，使用DTO投影）
  - `getQcBadItemsByLbIds()`: QC不良项目查询
  - `getQcBadItemsByLbIdsUsingTempTable()`: 临时表方式QC查询

### Service层
- `LabelWorkProcessAnalysisService.java`: 服务接口
- `LabelWorkProcessAnalysisServiceImpl.java`: 服务实现类

### Controller层
- `LabelWorkProcessAnalysisController.java`: REST API控制器
  - `GET /single-line`: 单线体查询
  - `POST /multiple-lines`: 多线体查询（新增）
  - `POST /multiple-lines-with-qc-bad-items`: 多线体查询带QC不良项目（新增）
  - `POST /qc-bad-items`: QC不良项目查询
  - `POST /qc-bad-items-temp-table`: 临时表方式QC查询

### 测试
- `LabelWorkProcessAnalysisControllerTest.java`: 控制器单元测试

### DTO层
- `LabelWorkProcessAnalysisDto.java`: 数据传输对象，用于查询结果投影

### 文档
- `LABEL_WORK_PROCESS_ANALYSIS_API_DOCUMENTATION.md`: API接口文档
- `DTO_PROJECTION_USAGE.md`: DTO投影使用说明

## 核心查询SQL

### 多线体查询SQL
```sql
SELECT
  A.LB_ID,
  B.LB_GRP,
  A.PL_ID,
  A.MO,
  MIN(CASE WHEN A.WP_ID IN ('SMT-03', 'SMT-06') THEN A.IS_PASS ELSE 'Y' END) AS KEY_WP_FIRST_RESULT,
  MAX(A.IS_PASS) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_RESULT,
  MAX(A.WP_CMP_DATE) KEEP (DENSE_RANK LAST ORDER BY A.WP_CMP_DATE) AS FINAL_WP_CMP_DATE
FROM
  TB_PM_MO_LBWP A
  INNER JOIN TB_PM_MO_LB B ON A.LB_ID = B.LB_ID
WHERE
    A.WP_CMP_DATE >= :startDate
    AND A.WP_CMP_DATE < :endDate
  AND A.PL_ID IN (:plIds)
GROUP BY
  A.LB_ID,
  B.LB_GRP,
  A.PL_ID,
  A.MO
HAVING
  MIN(CASE WHEN A.WP_ID IN ('SMT-03', 'SMT-06') THEN A.IS_PASS ELSE 'Y' END) = 'N'
ORDER BY
  FINAL_WP_CMP_DATE
```

## 主要特性

### 1. 多线体支持
- 支持同时查询多个线体的数据
- 自动处理Oracle IN子句1000参数限制
- 统一返回格式，便于前端处理

### 2. 查询优化
- **分批查询**: 当参数数量超过1000时自动分批
- **临时表查询**: 当LB_ID数量超过5000时使用临时表方式
- **智能选择**: 根据数据量自动选择最优查询策略

### 3. DTO投影优化
- **类型安全**: 避免Object[]数组的类型转换错误
- **性能优化**: 减少内存占用，只加载需要的字段
- **代码可维护性**: 清晰的数据传输对象结构

### 4. 错误处理
- 完善的异常处理机制
- 临时表自动清理
- 详细的错误日志记录

## 配置参数

### application-query.yml
```yaml
oee:
  query:
    # Oracle IN子句的最大参数数量
    oracle-in-clause-max-size: 1000
    
    # 使用临时表查询的阈值
    temp-table-threshold: 5000
    
    # 分批查询的批次大小
    batch-size: 1000
```

## 使用示例

### 1. 单线体查询
```bash
GET /oee-mes-server/label-work-process-analysis/single-line?plId=SMT1-1&startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
```

### 2. 多线体查询
```bash
POST /oee-mes-server/label-work-process-analysis/multiple-lines?startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
Content-Type: application/json

[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3",
  "SMT1-4",
  "SMT1-5",
  "SMT2-1",
  "SMT2-2",
  "SMT2-3",
  "SMT2-4",
  "SMT3-1",
  "SMT3-2",
  "SMT3-3",
  "SMT3-6",
  "SMT3-7",
  "LS101",
  "LS102",
  "LS103",
  "LS104",
  "LS105"
]
```

### 3. 多线体查询带QC不良项目（新增）
```bash
POST /oee-mes-server/label-work-process-analysis/multiple-lines-with-qc-bad-items?startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
Content-Type: application/json

[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3",
  "SMT1-4",
  "SMT1-5",
  "SMT2-1",
  "SMT2-2",
  "SMT2-3",
  "SMT2-4",
  "SMT3-1",
  "SMT3-2",
  "SMT3-3",
  "SMT3-6",
  "SMT3-7",
  "LS101",
  "LS102",
  "LS103",
  "LS104",
  "LS105"
]
```

### 4. QC不良项目查询
```bash
POST /oee-mes-server/label-work-process-analysis/qc-bad-items
Content-Type: application/json

[
  "38364648-001 51897217",
  "38364648-001 51897218"
]
```

## 性能说明
- **响应时间**: 通常 < 3秒（取决于数据量和线体数量）
- **数据量限制**: 建议单次查询时间范围不超过24小时
- **线体数量**: 多线体查询建议不超过50个线体
- **并发支持**: 支持多用户并发访问

## 注意事项
1. 日期参数必须使用 "yyyy-MM-dd HH:mm:ss" 格式
2. 多线体查询使用POST方法，线体ID列表通过请求体传递
3. 只返回关键工序（SMT-03, SMT-06）失败的标签数据
4. QC不良项目查询会根据数据量自动选择最优策略
5. 建议在生产环境中添加适当的缓存机制

## 版本历史
- **v1.0** (2025-07-12): 初始版本，支持单线体和多线体查询功能
