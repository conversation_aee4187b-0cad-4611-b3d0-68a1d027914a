# 标签工艺过程分析API接口文档

## 接口概述
该模块提供标签工艺过程分析相关的REST API接口，包括单线体查询、多线体查询以及QC不良项目查询功能。

## 基础信息
- **基础路径**: `/oee-mes-server/label-work-process-analysis`
- **响应格式**: application/json
- **接口版本**: v1.0

## 接口列表

### 1. 单线体标签工艺过程分析查询

#### 接口信息
- **接口路径**: `/single-line`
- **请求方法**: GET
- **功能描述**: 根据单个线体ID和日期范围查询标签工艺过程分析数据

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-12 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-12 08:00:00 |

#### 请求示例
```bash
GET /oee-mes-server/label-work-process-analysis/single-line?plId=SMT1-1&startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
```

### 2. 多线体标签工艺过程分析查询（新增）

#### 接口信息
- **接口路径**: `/multiple-lines`
- **请求方法**: POST
- **功能描述**: 根据多个线体ID和日期范围查询标签工艺过程分析数据

#### 请求参数
**URL参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| startDate | String | 是 | 开始日期时间 | 2025-07-12 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-12 08:00:00 |

**请求体**:
```json
[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3",
  "SMT1-4",
  "SMT1-5",
  "SMT2-1",
  "SMT2-2",
  "SMT2-3",
  "SMT2-4",
  "SMT3-1",
  "SMT3-2",
  "SMT3-3",
  "SMT3-6",
  "SMT3-7",
  "LS101",
  "LS102",
  "LS103",
  "LS104",
  "LS105"
]
```

#### 请求示例
```bash
POST /oee-mes-server/label-work-process-analysis/multiple-lines?startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
Content-Type: application/json

[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3"
]
```

### 3. QC不良项目查询

#### 接口信息
- **接口路径**: `/qc-bad-items`
- **请求方法**: POST
- **功能描述**: 根据LB_ID列表查询QC不良项目信息

#### 请求体
```json
[
  "38364648-001 51897217",
  "38364648-001 51897218",
  "38364648-001 51897219"
]
```

### 4. QC不良项目查询（临时表方式）

#### 接口信息
- **接口路径**: `/qc-bad-items-temp-table`
- **请求方法**: POST
- **功能描述**: 使用临时表方式查询QC不良项目信息，适用于大量LB_ID的场景

#### 请求体
```json
[
  "38364648-001 51897217",
  "38364648-001 51897218"
]
```

### 5. 多线体标签工艺过程分析与QC不良项目组合查询（新增）

#### 接口信息
- **接口路径**: `/multiple-lines-with-qc-bad-items`
- **请求方法**: POST
- **功能描述**: 根据多个线体ID和日期范围查询标签工艺过程分析数据，并包含对应的QC不良项目信息

#### 请求参数
**URL参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| startDate | String | 是 | 开始日期时间 | 2025-07-12 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-12 08:00:00 |

**请求体**:
```json
[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3",
  "SMT1-4",
  "SMT1-5",
  "SMT2-1",
  "SMT2-2",
  "SMT2-3",
  "SMT2-4",
  "SMT3-1",
  "SMT3-2",
  "SMT3-3",
  "SMT3-6",
  "SMT3-7",
  "LS101",
  "LS102",
  "LS103",
  "LS104",
  "LS105"
]
```

#### 请求示例
```bash
POST /oee-mes-server/label-work-process-analysis/multiple-lines-with-qc-bad-items?startDate=2025-07-12%2008:00:00&endDate=2025-07-12%2008:00:00
Content-Type: application/json

[
  "SMT1-1",
  "SMT1-2",
  "SMT1-3"
]
```

## 响应格式

### 标签工艺过程分析响应
```json
[
  {
    "lbId": "38364648-001 51897217",
    "lbGrp": "GRP001",
    "plId": "SMT1-1",
    "mo": "MO202507120001",
    "keyWpFirstResult": "N",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-12T10:30:00.000+00:00"
  }
]
```

### QC不良项目响应
```json
[
  {
    "mo": "MO202507120001",
    "plId": "SMT1-1",
    "lbId": "38364648-001 51897217",
    "badItemId": "SOLDER_BRIDGE",
    "badPoint": "U1",
    "fieldEx2": "严重"
  }
]
```

### 多线体标签工艺过程分析与QC不良项目组合响应
```json
[
  {
    "lbId": "38364648-001 51897217",
    "lbGrp": "GRP001",
    "plId": "SMT1-1",
    "mo": "MO202507120001",
    "keyWpFirstResult": "N",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-12T10:30:00.000+00:00",
    "qcBadItems": [
      {
        "mo": "MO202507120001",
        "plId": "SMT1-1",
        "lbId": "38364648-001 51897217",
        "badItemId": "SOLDER_BRIDGE",
        "badPoint": "U1",
        "fieldEx2": "严重"
      },
      {
        "mo": "MO202507120001",
        "plId": "SMT1-1",
        "lbId": "38364648-001 51897217",
        "badItemId": "MISSING_COMPONENT",
        "badPoint": "R5",
        "fieldEx2": "一般"
      }
    ]
  },
  {
    "lbId": "38364648-001 51897218",
    "lbGrp": "GRP001",
    "plId": "SMT1-2",
    "mo": "MO202507120001",
    "keyWpFirstResult": "Y",
    "finalResult": "N",
    "finalWpCmpDate": "2025-07-12T11:15:00.000+00:00",
    "qcBadItems": []
  }
]
```

## 响应字段说明

### 标签工艺过程分析字段
| 字段名 | 类型 | 描述 | 可能值 |
|--------|------|------|--------|
| lbId | String | 标签ID | 唯一标识符 |
| lbGrp | String | 标签组 | 标签分组信息 |
| plId | String | 线体ID | 生产线标识 |
| mo | String | 制造订单号 | 生产订单编号 |
| keyWpFirstResult | String | 关键工序首次结果 | Y(通过)/N(失败) |
| finalResult | String | 最终结果 | Y(通过)/N(失败) |
| finalWpCmpDate | DateTime | 最终工序完成时间 | ISO 8601格式 |

### QC不良项目字段
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| mo | String | 制造订单号 | MO202507120001 |
| plId | String | 线体ID | SMT1-1 |
| lbId | String | 标签ID | 38364648-001 51897217 |
| badItemId | String | 不良项目ID | SOLDER_BRIDGE |
| badPoint | String | 不良点位 | U1 |
| fieldEx2 | String | 扩展字段2 | 严重/一般/轻微 |

## 查询逻辑说明

### 关键工序筛选
- 只返回关键工序（SMT-03, SMT-06）失败的标签
- 使用 `KEY_WP_FIRST_RESULT = 'N'` 进行筛选

### 多线体查询特点
- 支持同时查询多个线体的数据
- 自动处理Oracle IN子句1000参数限制
- 按最终工序完成时间排序
- 使用DTO投影优化查询性能和类型安全

### QC查询优化
- 自动选择最优查询策略（分批查询 vs 临时表查询）
- 当LB_ID数量超过5000时自动使用临时表方式
- 支持手动指定查询方式

### 组合查询特点
- 一次请求获取标签工艺过程分析和QC不良项目数据
- 自动关联LB_ID，减少客户端数据处理复杂度
- 支持多线体批量查询，提高查询效率

## 性能说明
- **响应时间**: 通常 < 3秒（取决于数据量和线体数量）
- **数据量限制**: 建议单次查询时间范围不超过24小时
- **并发支持**: 支持多用户并发访问
- **线体数量**: 多线体查询建议不超过50个线体
- **组合查询**: 组合查询响应时间可能稍长，建议合理控制查询范围

## 注意事项
1. 日期参数必须使用 "yyyy-MM-dd HH:mm:ss" 格式
2. 多线体查询使用POST方法，线体ID列表通过请求体传递
3. 只返回关键工序失败的标签数据
4. QC不良项目查询会根据数据量自动选择最优策略
5. 组合查询接口会自动关联标签工艺过程分析和QC不良项目数据
6. 如果某个标签没有QC不良项目，qcBadItems字段将为空数组
7. 建议在生产环境中添加适当的缓存机制

## 错误处理
- 400: 请求参数错误（日期格式错误、必填参数缺失等）
- 500: 服务器内部错误（数据库连接失败、SQL执行错误等）

## 版本历史
- **v1.0** (2025-07-12): 初始版本，支持单线体和多线体查询功能
