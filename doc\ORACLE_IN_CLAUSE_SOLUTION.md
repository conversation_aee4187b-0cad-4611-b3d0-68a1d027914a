# Oracle IN子句1000参数限制解决方案

## 问题描述
Oracle数据库的IN子句最多只能包含1000个参数，当查询的LB_ID数量超过1000个时，会出现以下错误：
```
ORA-01795: 列表中的最大表达式数为 1000
```

## 解决方案

### 方案1：分批查询（推荐）
将大量的LB_ID分成多个批次，每批最多1000个，然后合并查询结果。

**优点**：
- 实现简单
- 性能稳定
- 适用于大多数场景

**缺点**：
- 需要多次数据库查询
- 当数据量特别大时性能可能下降

### 方案2：临时表查询（高级方案）
创建临时表存储LB_ID，然后通过JOIN查询。

**优点**：
- 只需要一次查询
- 适用于超大数据量
- 性能优秀

**缺点**：
- 实现复杂
- 需要临时表权限
- 可能存在并发问题

## 实现细节

### 1. 配置类
```java
@Configuration
@ConfigurationProperties(prefix = "oee.query")
public class QueryConfig {
    private int batchSize = 1000;
    private int tempTableThreshold = 5000;
    // ... getters and setters
}
```

### 2. 分批查询实现
```java
public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
    final int BATCH_SIZE = queryConfig.getBatchSize();
    List<QcBadItem> allResults = new ArrayList<>();
    
    for (int i = 0; i < lbIds.size(); i += BATCH_SIZE) {
        int endIndex = Math.min(i + BATCH_SIZE, lbIds.size());
        List<String> batchLbIds = lbIds.subList(i, endIndex);
        // 执行批次查询
        // ...
    }
    return allResults;
}
```

### 3. 智能选择策略
```java
@Override
public List<QcBadItem> getQcBadItemsByLbIds(List<String> lbIds) {
    if (lbIds != null && lbIds.size() > queryConfig.getTempTableThreshold()) {
        // 大量数据使用临时表方式
        return repository.getQcBadItemsByLbIdsUsingTempTable(lbIds);
    } else {
        // 少量数据使用分批查询方式
        return repository.getQcBadItemsByLbIds(lbIds);
    }
}
```

## 配置参数

### application-query.yml
```yaml
oee:
  query:
    # Oracle IN子句的最大参数数量
    oracle-in-clause-max-size: 1000
    
    # 使用临时表查询的阈值
    temp-table-threshold: 5000
    
    # 分批查询的批次大小
    batch-size: 1000
```

## 性能对比

| 数据量 | 分批查询 | 临时表查询 | 推荐方案 |
|--------|----------|------------|----------|
| < 1000 | ✅ 快速 | ❌ 过度设计 | 分批查询 |
| 1000-5000 | ✅ 良好 | ⚠️ 可选 | 分批查询 |
| 5000-10000 | ⚠️ 较慢 | ✅ 快速 | 临时表查询 |
| > 10000 | ❌ 很慢 | ✅ 快速 | 临时表查询 |

## 使用建议

### 1. 默认配置
- 批次大小：1000（Oracle限制）
- 临时表阈值：5000（经验值）

### 2. 生产环境调优
根据实际业务场景调整参数：
- 如果数据库性能较好，可以适当增加批次大小
- 如果经常处理大量数据，可以降低临时表阈值

### 3. 监控建议
- 监控查询响应时间
- 监控数据库连接池使用情况
- 记录大数据量查询的日志

## 注意事项

### 1. 事务管理
- 分批查询需要注意事务边界
- 临时表查询需要确保事务提交后清理

### 2. 内存使用
- 大量数据查询时注意JVM内存使用
- 考虑使用流式处理减少内存占用

### 3. 并发安全
- 临时表方案需要考虑并发访问
- 建议使用会话级临时表

## 测试验证

### 1. 单元测试
```java
@Test
public void testLargeDataQuery() {
    List<String> largeLbIds = generateLbIds(2000);
    List<QcBadItem> result = service.getQcBadItemsByLbIds(largeLbIds);
    assertNotNull(result);
}
```

### 2. 性能测试
- 测试不同数据量下的响应时间
- 测试并发访问的稳定性
- 测试内存使用情况

## 总结
通过实施分批查询和临时表查询的组合方案，成功解决了Oracle IN子句1000参数限制的问题。系统会根据数据量自动选择最优的查询策略，确保在各种场景下都能提供良好的性能。
