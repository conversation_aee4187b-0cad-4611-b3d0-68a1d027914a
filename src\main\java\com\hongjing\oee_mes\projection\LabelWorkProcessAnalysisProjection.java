package com.hongjing.oee_mes.projection;

import java.util.Date;

/**
 * 标签工艺过程分析投影接口 使用Spring Data JPA的接口投影，提供类型安全和性能优化
 */
public interface LabelWorkProcessAnalysisProjection {

	/**
	 * 获取标签ID
	 * @return 标签ID
	 */
	String getLbId();

	/**
	 * 获取标签组
	 * @return 标签组
	 */
	String getLbGrp();

	/**
	 * 获取线体ID
	 * @return 线体ID
	 */
	String getPlId();

	/**
	 * 获取制造订单号
	 * @return 制造订单号
	 */
	String getMo();

	/**
	 * 获取关键工序首次结果
	 * @return 关键工序首次结果
	 */
	String getKeyWpFirstResult();

	/**
	 * 获取最终结果
	 * @return 最终结果
	 */
	String getFinalResult();

	/**
	 * 获取最终工序完成时间
	 * @return 最终工序完成时间
	 */
	Date getFinalWpCmpDate();

	/**
	 * 转换为领域实体
	 * @return LabelWorkProcessAnalysis实体
	 */
	default com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis toEntity() {
		return new com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis(getLbId(), getLbGrp(), getPlId(), getMo(),
				getKeyWpFirstResult(), getFinalResult(), getFinalWpCmpDate());
	}

}
