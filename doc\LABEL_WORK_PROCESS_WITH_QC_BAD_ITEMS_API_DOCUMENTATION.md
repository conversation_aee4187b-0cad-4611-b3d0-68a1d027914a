# 标签工艺过程分析与QC不良项目组合接口文档

## 接口概述
该接口提供标签工艺过程分析与QC不良项目的组合查询功能。首先根据线体ID和日期范围查询关键工序失败的标签，然后根据这些标签的LB_ID查询对应的QC不良项目信息，最终返回包含完整质量分析数据的组合结果。

## 接口信息
- **接口名称**: 标签工艺过程分析与QC不良项目组合查询
- **接口路径**: `/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items`
- **请求方法**: GET
- **响应格式**: application/json
- **接口版本**: v1.0

## 请求参数

### URL参数
| 参数名 | 类型 | 必填 | 描述 | 示例值 | 备注 |
|--------|------|------|------|--------|------|
| plId | String | 是 | 线体ID | SMT1-1 | 生产线标识符 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 | 格式：yyyy-MM-dd HH:mm:ss |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 | 格式：yyyy-MM-dd HH:mm:ss |

### 请求示例
```http
GET /oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00
Host: your-server.com
Accept: application/json
```

## 响应数据

### 成功响应 (200 OK)
```json
[
    {
        "lbId": "38364648-001 51897217",
        "lbGrp": "GRP001",
        "plId": "SMT1-1",
        "mo": "MO202507090001",
        "keyWpFirstResult": "N",
        "finalResult": "N",
        "finalWpCmpDate": "2025-07-08T10:30:00.000+00:00",
        "qcBadItems": [
            {
                "mo": "MO202507090001",
                "plId": "SMT1-1",
                "lbId": "38364648-001 51897217",
                "badItemId": "SOLDER_BRIDGE",
                "badPoint": "U1",
                "fieldEx2": "严重"
            },
            {
                "mo": "MO202507090001",
                "plId": "SMT1-1",
                "lbId": "38364648-001 51897217",
                "badItemId": "MISSING_COMPONENT",
                "badPoint": "R5",
                "fieldEx2": "一般"
            }
        ]
    },
    {
        "lbId": "38364648-001 51897218",
        "lbGrp": "GRP001",
        "plId": "SMT1-1",
        "mo": "MO202507090001",
        "keyWpFirstResult": "Y",
        "finalResult": "N",
        "finalWpCmpDate": "2025-07-08T11:15:00.000+00:00",
        "qcBadItems": []
    }
]
```

### 响应字段说明

#### 主要字段（标签工艺过程分析）
| 字段名 | 类型 | 描述 | 可能值 |
|--------|------|------|--------|
| lbId | String | 标签ID | 唯一标识符 |
| lbGrp | String | 标签组 | 标签分组信息 |
| plId | String | 线体ID | 生产线标识 |
| mo | String | 制造订单号 | 生产订单编号 |
| keyWpFirstResult | String | 关键工序首次结果 | Y(通过)/N(失败) |
| finalResult | String | 最终结果 | Y(通过)/N(失败) |
| finalWpCmpDate | DateTime | 最终工序完成时间 | ISO 8601格式 |
| qcBadItems | Array | QC不良项目列表 | 可能为空数组 |

#### QC不良项目字段
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| mo | String | 制造订单号 | MO202507090001 |
| plId | String | 线体ID | SMT1-1 |
| lbId | String | 标签ID | 38364648-001 51897217 |
| badItemId | String | 不良项目ID | SOLDER_BRIDGE |
| badPoint | String | 不良点位 | U1 |
| fieldEx2 | String | 扩展字段2 | 严重/一般/轻微 |

## 错误响应

### 400 Bad Request - 参数错误
```json
{
    "timestamp": "2025-07-09T10:30:00.000+00:00",
    "status": 400,
    "error": "Bad Request",
    "message": "日期格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式",
    "path": "/oee/label-work-process-analysis-with-qc-bad-items"
}
```

### 500 Internal Server Error - 服务器错误
```json
{
    "timestamp": "2025-07-09T10:30:00.000+00:00",
    "status": 500,
    "error": "Internal Server Error",
    "message": "数据库连接异常",
    "path": "/oee/label-work-process-analysis-with-qc-bad-items"
}
```

## 业务逻辑说明

### 查询流程
1. **第一步**: 根据线体ID和日期范围查询关键工序失败的标签
   - 查询条件：关键工序（SMT-03, SMT-06）中至少有一个失败
   - 数据来源：TB_PM_MO_LBWP + TB_PM_MO_LB
2. **第二步**: 提取所有LB_ID
3. **第三步**: 根据LB_ID列表查询QC不良项目
   - 数据来源：TB_PM_QC_HD + TB_PM_QC_DT
4. **第四步**: 数据组合和关联

### 筛选规则
- 只返回关键工序（SMT-03, SMT-06）失败的标签
- 按最终工序完成时间排序
- 支持一对多关系（一个标签可能有多个QC不良项目）

## 调用示例

### cURL示例
```bash
curl -X GET \
  "http://your-server.com/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00" \
  -H "Accept: application/json"
```

### Java示例
```java
// 使用RestTemplate
String url = "http://your-server.com/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items";
UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
    .queryParam("plId", "SMT1-1")
    .queryParam("startDate", "2025-07-08 08:00:00")
    .queryParam("endDate", "2025-07-09 08:00:00");

ResponseEntity<LabelWorkProcessWithQcBadItems[]> response = 
    restTemplate.getForEntity(builder.toUriString(), LabelWorkProcessWithQcBadItems[].class);
```

### JavaScript示例
```javascript
const params = new URLSearchParams({
    plId: 'SMT1-1',
    startDate: '2025-07-08 08:00:00',
    endDate: '2025-07-09 08:00:00'
});

fetch(`http://your-server.com/oee-mes-server/oee/label-work-process-analysis-with-qc-bad-items?${params}`)
    .then(response => response.json())
    .then(data => console.log(data))
    .catch(error => console.error('Error:', error));
```

## 使用场景

### 质量分析场景
- **综合质量分析**: 同时查看工艺过程和质量检测数据
- **根因分析**: 通过组合数据进行问题根因分析
- **质量报表**: 生成包含完整质量信息的报表

### 生产监控场景
- **实时监控**: 监控特定时间段的质量问题
- **问题追踪**: 追踪失败标签的详细信息
- **趋势分析**: 分析质量问题的时间分布

## 性能说明
- **响应时间**: 通常 < 2秒（取决于数据量）
- **数据量限制**: 建议单次查询时间范围不超过24小时
- **并发支持**: 支持多用户并发访问

## 注意事项
1. 日期参数必须使用 "yyyy-MM-dd HH:mm:ss" 格式
2. 只返回关键工序失败的标签，非关键工序失败不会出现在结果中
3. QC不良项目可能为空，表示虽然工艺过程失败但没有QC记录
4. 返回结果按最终工序完成时间升序排列
5. 建议在生产环境中添加适当的缓存机制

## 版本历史
- **v1.0** (2025-07-09): 初始版本，支持基本的组合查询功能
