package com.hongjing.oee_mes.dto;

import java.util.Date;

/**
 * 标签工艺过程分析DTO投影类 用于JPA原生查询的结果映射
 */
public class LabelWorkProcessAnalysisDto {

	private String lbId;

	private String lbGrp;

	private String plId;

	private String mo;

	private String keyWpFirstResult;

	private String finalResult;

	private Date finalWpCmpDate;

	// JPA投影构造函数
	public LabelWorkProcessAnalysisDto(String lbId, String lbGrp, String plId, String mo, String keyWpFirstResult,
			String finalResult, Date finalWpCmpDate) {
		this.lbId = lbId;
		this.lbGrp = lbGrp;
		this.plId = plId;
		this.mo = mo;
		this.keyWpFirstResult = keyWpFirstResult;
		this.finalResult = finalResult;
		this.finalWpCmpDate = finalWpCmpDate;
	}

	// 默认构造函数
	public LabelWorkProcessAnalysisDto() {
	}

	// 转换为实体类的方法
	public com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis toEntity() {
		return new com.hongjing.oee_mes.domain.LabelWorkProcessAnalysis(this.lbId, this.lbGrp, this.plId, this.mo,
				this.keyWpFirstResult, this.finalResult, this.finalWpCmpDate);
	}

	// Getters and Setters
	public String getLbId() {
		return lbId;
	}

	public void setLbId(String lbId) {
		this.lbId = lbId;
	}

	public String getLbGrp() {
		return lbGrp;
	}

	public void setLbGrp(String lbGrp) {
		this.lbGrp = lbGrp;
	}

	public String getPlId() {
		return plId;
	}

	public void setPlId(String plId) {
		this.plId = plId;
	}

	public String getMo() {
		return mo;
	}

	public void setMo(String mo) {
		this.mo = mo;
	}

	public String getKeyWpFirstResult() {
		return keyWpFirstResult;
	}

	public void setKeyWpFirstResult(String keyWpFirstResult) {
		this.keyWpFirstResult = keyWpFirstResult;
	}

	public String getFinalResult() {
		return finalResult;
	}

	public void setFinalResult(String finalResult) {
		this.finalResult = finalResult;
	}

	public Date getFinalWpCmpDate() {
		return finalWpCmpDate;
	}

	public void setFinalWpCmpDate(Date finalWpCmpDate) {
		this.finalWpCmpDate = finalWpCmpDate;
	}

	@Override
	public String toString() {
		return "LabelWorkProcessAnalysisDto{" + "lbId='" + lbId + '\'' + ", lbGrp='" + lbGrp + '\'' + ", plId='" + plId
				+ '\'' + ", mo='" + mo + '\'' + ", keyWpFirstResult='" + keyWpFirstResult + '\'' + ", finalResult='"
				+ finalResult + '\'' + ", finalWpCmpDate=" + finalWpCmpDate + '}';
	}

}
